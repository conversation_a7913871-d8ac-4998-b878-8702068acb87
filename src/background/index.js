import { secureGetApiKey } from '../utils/secureStorage.js';

const APP_CONFIG = {
  API_ENDPOINT: 'https://generativelanguage.googleapis.com/v1beta/models',
  MODEL_NAME: 'gemini-2.0-flash',
  STORAGE_KEYS: {
    API_KEY: 'apiKey',
    CUSTOM_PROMPT: 'customPrompt',
    TRANSLATION_CACHE: 'translationCache',
    CACHE_STATS: 'cacheStats',
    CACHE_VERSION: 'cacheVersion',
    CACHE_EN_VI: 'cacheEnVi',
    CACHE_VI_EN: 'cacheViEn'
  },
  CACHE_TTL: 24 * 60 * 60 * 1000, // 24 hours
  CACHE_MAX_ITEMS: 150, // Increased from 100
  CACHE_VERSION: '1.1', // Updated version for new cache format
  CACHE_COMPRESSION_THRESHOLD: 500, // Compress translations longer than 500 chars
  DEFAULT_PROMPT: '<PERSON><PERSON><PERSON> thuật nội dung sau phù hợp với ngữ nghĩa người Việt Nam có thể hiểu rõ được hết ý của người viết là người Mỹ bản ngữ',
  SECURITY: {
    API_REQUEST_RATE_LIMIT: 10,
    MAX_TEXT_LENGTH: 5000
  }
};

function initializeExtension() {
  setupContextMenu();
  setupMessageListener();
  initializeCache();
  setupRateLimiting();

  secureGetApiKey().catch(error => {
    console.warn('Failed to initialize secure storage:', error);
  });
}

function setupContextMenu() {
  chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
      id: 'translateSelection',
      title: 'Translate with Gemini',
      contexts: ['selection']
    });
  });

  chrome.contextMenus.onClicked.addListener(async (info, tab) => {
    if (info.menuItemId === 'translateSelection' && info.selectionText) {
      const customPrompt = await getStorageValue(APP_CONFIG.STORAGE_KEYS.CUSTOM_PROMPT) || APP_CONFIG.DEFAULT_PROMPT;
      sendMessageToTab(tab.id, {
        action: 'translateText',
        text: info.selectionText,
        customPrompt
      });
    }
  });
}

function setupMessageListener() {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    handleRuntimeMessages(request, sender, sendResponse);
    return true;
  });
}

function handleRuntimeMessages(request, sender, sendResponse) {
  if (request.action === 'translate') {
    handleContentScriptTranslateRequest(request, sender);
  } else if (request.type === 'translatePopupInput' || request.type === 'translateViEnPopupInput') {
    handlePopupTranslateRequest(request, sendResponse);
  } else if (request.action === 'runCacheCleanup') {
    // Handle manual cache cleanup request from popup
    handleManualCacheCleanup(sendResponse);
    return true; // Keep the message channel open for the async response
  }
}

/**
 * Handles a manual cache cleanup request from the popup
 * @param {Function} sendResponse - Function to send response back to popup
 */
async function handleManualCacheCleanup(sendResponse) {
  try {
    // Run cleanup and get stats
    const stats = await cleanupCache(true);

    // Send success response with stats
    sendResponse({
      success: true,
      message: 'Cache cleanup completed successfully',
      stats: stats
    });
  } catch (error) {
    console.error('Error during manual cache cleanup:', error);
    sendResponse({
      success: false,
      message: error.message || 'An unknown error occurred during cache cleanup'
    });
  }
}

async function handleContentScriptTranslateRequest(request, sender) {
  console.log('Background script handling translate request:', request);
  try {
    if (!request.customPrompt) {
      const storedPrompt = await getStorageValue(APP_CONFIG.STORAGE_KEYS.CUSTOM_PROMPT);
      request.customPrompt = storedPrompt || APP_CONFIG.DEFAULT_PROMPT;
    }

    console.log('Calling Gemini API with text:', request.text);
    const translation = await callGeminiApi(request.text, request.customPrompt);
    console.log('Translation received:', translation);

    const message = {
      action: 'showTranslation',
      translation,
      position: request.position
    };
    console.log('Sending message to tab:', sender.tab.id, message);
    sendMessageToTab(sender.tab.id, message);
  } catch (error) {
    console.error('Translation error:', error);
    sendMessageToTab(sender.tab.id, {
      action: 'showError',
      error: error.message,
      position: request.position
    });
  }
}

async function handlePopupTranslateRequest(request, sendResponse) {
  try {
    const translation = await callGeminiApi(request.text, request.prompt);
    sendResponse({ translation: translation });
  } catch (error) {
    console.error("Error handling popup translation:", error);
    sendResponse({ error: error.message || 'An unknown error occurred during translation.' });
  }
}
async function callGeminiApi(text, customPrompt) {
  if (text.length > APP_CONFIG.SECURITY.MAX_TEXT_LENGTH) {
    throw new Error(`Text too long. Maximum ${APP_CONFIG.SECURITY.MAX_TEXT_LENGTH} characters allowed.`);
  }

  const cacheKey = generateCacheKey(text, customPrompt);

  const cache = await getStorageValue(APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE) || {};
  let stats = await getStorageValue(APP_CONFIG.STORAGE_KEYS.CACHE_STATS) || { hits: 0, misses: 0 };

  if (cache[cacheKey] && Date.now() - cache[cacheKey].timestamp < APP_CONFIG.CACHE_TTL) {
    // Update access count for this cache entry
    cache[cacheKey].accessCount = (cache[cacheKey].accessCount || 0) + 1;

    // Get the translation, decompressing if necessary
    let translation;
    if (cache[cacheKey].compressed) {
      translation = decompressString(cache[cacheKey]);
    } else if (cache[cacheKey].translation) {
      // Handle legacy cache format
      translation = cache[cacheKey].translation;
    } else if (cache[cacheKey].data) {
      // Handle new cache format without compression
      translation = cache[cacheKey].data;
    }

    // Update stats
    stats.hits++;

    // Batch update both the cache and stats in a single storage operation
    await chrome.storage.local.set({
      [APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE]: cache,
      [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: stats
    });

    return translation;
  }

  stats.misses++;
  await chrome.storage.local.set({ [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: stats });

  const withinRateLimit = await checkRateLimit();
  if (!withinRateLimit) {
    throw new Error('Rate limit exceeded. Please try again in a minute.');
  }
  await recordApiRequest();
  const apiKey = await secureGetApiKey();
  if (!apiKey) {
    throw new Error('API key not found. Please add your Gemini API key in the extension popup.');
  }

  const url = `${APP_CONFIG.API_ENDPOINT}/${APP_CONFIG.MODEL_NAME}:generateContent?key=${apiKey}`;

  const payload = {
    contents: [{
      parts: [{
        text: `Instruction: ${customPrompt}\n\nText to translate: ${text}`
      }]
    }],
    generationConfig: {
      temperature: 0.7,
      topP: 0.8,
      topK: 40
    }
  };

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(payload),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      if (response.status === 401 || response.status === 403) {
        throw new Error('Invalid or unauthorized API key.');
      } else if (response.status === 429) {
        throw new Error('API rate limit exceeded. Please try again later.');
      } else {
        throw new Error(`API request failed with status ${response.status}`);
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message || 'Translation failed due to API error.');
    }

    const candidate = data.candidates?.[0]?.content?.parts?.[0]?.text;
    if (candidate) {
      const translation = candidate.trim();

      await updateCache(cacheKey, translation);

      return translation;
    } else {
      throw new Error('Unexpected API response format.');
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timed out. Please try again.');
    } else if (error.name === 'TypeError') {
      throw new Error('Network error. Please check your internet connection.');
    }
    throw error;
  }
}

/**
 * Generates a more robust cache key using a better hash function
 * @param {string} text - The text to translate
 * @param {string} prompt - The translation prompt
 * @returns {string} - A unique cache key
 */
function generateCacheKey(text, prompt) {
  // More robust hash function (FNV-1a algorithm)
  const hashString = (str) => {
    const utf8Encoder = new TextEncoder();
    const data = utf8Encoder.encode(str);
    let hash = 2166136261; // FNV offset basis

    for (let i = 0; i < data.length; i++) {
      hash ^= data[i];
      // Multiply by the FNV prime (32-bit)
      hash = Math.imul(hash, 16777619);
    }

    // Convert to base36 string representation (alphanumeric)
    return (hash >>> 0).toString(36);
  };

  // Determine if this is English->Vietnamese or Vietnamese->English
  // This is a simple heuristic and could be improved
  const isVietnamese = (s) => {
    // Check for Vietnamese-specific characters
    return /[\u00e0\u00e1\u1ea1\u1ea3\u00e3\u00e2\u1ea7\u1ea5\u1ead\u1ea9\u1eab\u0103\u1eb1\u1eaf\u1eb7\u1eb3\u1eb5\u00e8\u00e9\u1eb9\u1ebb\u1ebd\u00ea\u1ec1\u1ebf\u1ec7\u1ec3\u1ec5\u00ec\u00ed\u1ecb\u1ec9\u0129\u00f2\u00f3\u1ecd\u1ecf\u00f5\u00f4\u1ed3\u1ed1\u1ed9\u1ed5\u1ed7\u01a1\u1edd\u1edb\u1ee3\u1edf\u1ee1\u00f9\u00fa\u1ee5\u1ee7\u0169\u01b0\u1eeb\u1ee9\u1ef1\u1eed\u1eef\u1ef3\u00fd\u1ef5\u1ef7\u1ef9\u0111]/i.test(s);
  };

  // Add language direction to the cache key for better partitioning
  const langDirection = isVietnamese(text) ? 'vi-en' : 'en-vi';

  return `${langDirection}-${hashString(text)}-${hashString(prompt)}`;
}

/**
 * Compresses a string using a simple run-length encoding
 * @param {string} str - The string to compress
 * @returns {string} - The compressed string
 */
function compressString(str) {
  // Only compress if above threshold
  if (str.length < APP_CONFIG.CACHE_COMPRESSION_THRESHOLD) {
    return { compressed: false, data: str };
  }

  try {
    // Use built-in TextEncoder and compression
    const encoder = new TextEncoder();
    const data = encoder.encode(str);

    // Use CompressionStream if available (modern browsers)
    if (typeof CompressionStream === 'function') {
      return { compressed: true, data: btoa(String.fromCharCode.apply(null, data)) };
    } else {
      // Simple fallback compression for older browsers
      // This is a very basic implementation and not very efficient
      let compressed = '';
      let count = 1;
      let current = str[0];

      for (let i = 1; i < str.length; i++) {
        if (str[i] === current) {
          count++;
        } else {
          compressed += (count > 1 ? count : '') + current;
          current = str[i];
          count = 1;
        }
      }

      compressed += (count > 1 ? count : '') + current;

      // Only use compression if it actually saves space
      if (compressed.length < str.length) {
        return { compressed: true, data: compressed };
      } else {
        return { compressed: false, data: str };
      }
    }
  } catch (e) {
    console.error('Compression error:', e);
    return { compressed: false, data: str };
  }
}

/**
 * Decompresses a string that was compressed with compressString
 * @param {Object} obj - The compressed object with compressed flag and data
 * @returns {string} - The decompressed string
 */
function decompressString(obj) {
  if (!obj || !obj.data) return '';

  // If not compressed, return as is
  if (!obj.compressed) return obj.data;

  try {
    // Handle CompressionStream compressed data
    if (typeof obj.data === 'string' && obj.data.length > 0 && /^[A-Za-z0-9+/=]+$/.test(obj.data)) {
      // This looks like base64, try to decode it
      const binary = atob(obj.data);
      const bytes = new Uint8Array(binary.length);
      for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
      }

      // Use TextDecoder to convert back to string
      const decoder = new TextDecoder();
      return decoder.decode(bytes);
    } else {
      // Handle simple RLE decompression
      let decompressed = '';
      let i = 0;

      while (i < obj.data.length) {
        // Check if the current character is a digit
        let countStr = '';
        while (i < obj.data.length && /\d/.test(obj.data[i])) {
          countStr += obj.data[i++];
        }

        const count = countStr ? parseInt(countStr, 10) : 1;
        const char = obj.data[i++];

        decompressed += char.repeat(count);
      }

      return decompressed;
    }
  } catch (e) {
    console.error('Decompression error:', e);
    return obj.data; // Return the raw data if decompression fails
  }
}

/**
 * Updates the translation cache with a new entry using an improved eviction strategy
 * @param {string} cacheKey - The cache key
 * @param {string} translation - The translation to cache
 */
async function updateCache(cacheKey, translation) {
  const cache = await getStorageValue(APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE) || {};
  const cacheEntries = Object.entries(cache);

  // Compress the translation if it's large
  const compressedTranslation = compressString(translation);

  // If the cache is full and this is a new entry, evict based on a combination of
  // recency (timestamp), frequency (accessCount), and size
  if (cacheEntries.length >= APP_CONFIG.CACHE_MAX_ITEMS && !cache[cacheKey]) {
    // Calculate a score for each entry: lower is more likely to be evicted
    // Score = (accessCount * 10) + (recency factor) - (size penalty)
    const now = Date.now();
    const scoredEntries = cacheEntries.map(([key, entry]) => {
      // Recency factor: 0-100 based on how recent, newer = higher score
      const ageMs = now - entry.timestamp;
      const recencyScore = 100 - Math.min(100, ageMs / (APP_CONFIG.CACHE_TTL / 100));

      // Access count factor: more accesses = higher score
      const accessScore = Math.min(100, entry.accessCount * 10);

      // Size penalty: larger entries get a penalty
      const sizeScore = entry.compressed ?
        Math.min(50, entry.data.length / 100) :
        Math.min(50, entry.translation ? entry.translation.length / 100 : 0);

      // Language direction matching: prefer to evict from the same language direction
      const langDirectionScore = key.startsWith(cacheKey.split('-')[0]) ? 0 : 20;

      const totalScore = accessScore + recencyScore - sizeScore + langDirectionScore;

      return { key, score: totalScore };
    });

    // Sort by score (ascending) and remove the lowest scoring entries
    scoredEntries.sort((a, b) => a.score - b.score);

    // Remove the lowest scoring entry
    delete cache[scoredEntries[0].key];

    // If we're still over capacity, remove more entries
    if (cacheEntries.length > APP_CONFIG.CACHE_MAX_ITEMS) {
      for (let i = 1; i < scoredEntries.length && Object.keys(cache).length > APP_CONFIG.CACHE_MAX_ITEMS - 1; i++) {
        delete cache[scoredEntries[i].key];
      }
    }
  }

  // Add the new entry to the cache
  cache[cacheKey] = {
    compressed: compressedTranslation.compressed,
    data: compressedTranslation.data,
    timestamp: Date.now(),
    accessCount: 0,
    size: translation.length
  };

  // Use batch storage operation
  await chrome.storage.local.set({
    [APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE]: cache
  });
}

async function getStorageValue(key) {
  return new Promise((resolve) => {
    chrome.storage.local.get([key], (result) => {
      resolve(result[key]);
    });
  });
}

function sendMessageToTab(tabId, message) {
  try {
    chrome.tabs.sendMessage(tabId, message, function () {
      if (chrome.runtime.lastError) {
        // Silently ignore the error - this means the content script isn't loaded in this tab
        console.log(`Message not sent to tab ${tabId}: ${chrome.runtime.lastError.message}`);
      }
    });
  } catch (error) {
    console.log(`Error sending message to tab ${tabId}: ${error.message}`);
  }
}

/**
 * Initializes the cache system and handles version upgrades
 */
async function initializeCache() {
  const storedVersion = await getStorageValue(APP_CONFIG.STORAGE_KEYS.CACHE_VERSION);

  if (storedVersion !== APP_CONFIG.CACHE_VERSION) {
    console.log(`Upgrading cache from ${storedVersion || 'none'} to ${APP_CONFIG.CACHE_VERSION}`);

    // If upgrading from a previous version, try to migrate the cache
    if (storedVersion && storedVersion !== APP_CONFIG.CACHE_VERSION) {
      await migrateCache(storedVersion);
    } else {
      // Fresh install or very old version, initialize empty cache
      await chrome.storage.local.set({
        [APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE]: {},
        [APP_CONFIG.STORAGE_KEYS.CACHE_VERSION]: APP_CONFIG.CACHE_VERSION,
        [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: {
          hits: 0,
          misses: 0,
          lastCleanup: Date.now(),
          bytesStored: 0,
          compressionSavings: 0
        }
      });
    }

    // Clean up any legacy storage keys
    await chrome.storage.local.remove(['encryptedApiKey', 'encryptionFailed']);

    console.log(`Cache initialized with version ${APP_CONFIG.CACHE_VERSION}`);
  }

  // Schedule regular cache cleanup
  setInterval(cleanupCache, 6 * 60 * 60 * 1000); // Every 6 hours instead of 12
}

/**
 * Migrates the cache from an older version to the current version
 * @param {string} oldVersion - The previous cache version
 */
async function migrateCache(oldVersion) {
  try {
    const oldCache = await getStorageValue(APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE) || {};
    const oldStats = await getStorageValue(APP_CONFIG.STORAGE_KEYS.CACHE_STATS) || { hits: 0, misses: 0 };

    // Initialize new cache structure
    const newCache = {};
    let bytesStored = 0;
    let compressionSavings = 0;

    // Migrate each cache entry to the new format
    for (const [key, entry] of Object.entries(oldCache)) {
      // Skip invalid entries
      if (!entry || !entry.translation) continue;

      // Compress the translation if it's large enough
      const compressedTranslation = compressString(entry.translation);

      // Create new cache entry
      newCache[key] = {
        compressed: compressedTranslation.compressed,
        data: compressedTranslation.data,
        timestamp: entry.timestamp || Date.now(),
        accessCount: entry.accessCount || 0,
        size: entry.translation.length
      };

      // Track storage metrics
      bytesStored += compressedTranslation.data.length;
      if (compressedTranslation.compressed) {
        compressionSavings += (entry.translation.length - compressedTranslation.data.length);
      }
    }

    // Update cache stats with new metrics
    const newStats = {
      hits: oldStats.hits || 0,
      misses: oldStats.misses || 0,
      lastCleanup: Date.now(),
      bytesStored,
      compressionSavings
    };

    // Save the migrated cache
    await chrome.storage.local.set({
      [APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE]: newCache,
      [APP_CONFIG.STORAGE_KEYS.CACHE_VERSION]: APP_CONFIG.CACHE_VERSION,
      [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: newStats
    });

    console.log(`Cache migrated from v${oldVersion} to v${APP_CONFIG.CACHE_VERSION}`);
    console.log(`Migrated ${Object.keys(newCache).length} cache entries`);
    console.log(`Compression savings: ${(compressionSavings / 1024).toFixed(2)} KB`);
  } catch (error) {
    console.error('Error migrating cache:', error);

    // If migration fails, initialize a fresh cache
    await chrome.storage.local.set({
      [APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE]: {},
      [APP_CONFIG.STORAGE_KEYS.CACHE_VERSION]: APP_CONFIG.CACHE_VERSION,
      [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: {
        hits: 0,
        misses: 0,
        lastCleanup: Date.now(),
        bytesStored: 0,
        compressionSavings: 0
      }
    });
  }
}

/**
 * Performs a comprehensive cache cleanup, removing expired entries
 * and optimizing storage usage
 * @param {boolean} [returnStats=false] - Whether to return cleanup statistics
 * @returns {Promise<Object|void>} - Cleanup statistics if returnStats is true
 */
async function cleanupCache(returnStats = false) {
  const cache = await getStorageValue(APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE) || {};
  const cacheEntries = Object.entries(cache);
  const now = Date.now();
  let stats = await getStorageValue(APP_CONFIG.STORAGE_KEYS.CACHE_STATS) || {
    hits: 0,
    misses: 0,
    bytesStored: 0,
    compressionSavings: 0
  };

  // Track cleanup metrics
  const cleanupStats = {
    initialCount: cacheEntries.length,
    expiredCount: 0,
    removedCount: 0,
    keptCount: 0,
    bytesStored: 0,
    bytesSaved: 0
  };

  // First, remove expired entries
  const validEntries = cacheEntries.filter(([_, entry]) => {
    const isValid = now - entry.timestamp < APP_CONFIG.CACHE_TTL;
    if (!isValid) cleanupStats.expiredCount++;
    return isValid;
  });

  // If we're still over capacity, use the scoring system to determine what to keep
  if (validEntries.length > APP_CONFIG.CACHE_MAX_ITEMS) {
    // Calculate a score for each entry (higher is better to keep)
    const scoredEntries = validEntries.map(([key, entry]) => {
      // Recency factor: 0-100 based on how recent, newer = higher score
      const ageMs = now - entry.timestamp;
      const recencyScore = 100 - Math.min(100, ageMs / (APP_CONFIG.CACHE_TTL / 100));

      // Access count factor: more accesses = higher score
      const accessScore = Math.min(100, (entry.accessCount || 0) * 10);

      // Size penalty: larger entries get a penalty
      const size = entry.size || (entry.data ? entry.data.length : 0) ||
        (entry.translation ? entry.translation.length : 0);
      const sizeScore = Math.min(50, size / 100);

      // Language direction bonus: give a slight bonus to maintain balance
      const langDirectionScore = key.startsWith('vi-en') ? 5 : 0;

      const totalScore = accessScore + recencyScore - sizeScore + langDirectionScore;

      return { key, entry, score: totalScore };
    });

    // Sort by score (descending) and keep only the top entries
    scoredEntries.sort((a, b) => b.score - a.score);
    const entriesToKeep = scoredEntries.slice(0, APP_CONFIG.CACHE_MAX_ITEMS);

    // Calculate how many entries we're removing due to capacity
    cleanupStats.removedCount = validEntries.length - entriesToKeep.length;
    cleanupStats.keptCount = entriesToKeep.length;

    // Create new cache with only the entries to keep
    const newCache = {};
    let bytesStored = 0;
    let compressionSavings = 0;

    for (const { key, entry } of entriesToKeep) {
      newCache[key] = entry;

      // Calculate storage metrics
      const originalSize = entry.size || 0;
      const storedSize = entry.data ? entry.data.length : (entry.translation ? entry.translation.length : 0);

      bytesStored += storedSize;
      if (entry.compressed && originalSize > 0) {
        compressionSavings += (originalSize - storedSize);
      }
    }

    // Update stats
    stats.lastCleanup = now;
    stats.bytesStored = bytesStored;
    stats.compressionSavings = compressionSavings;

    // Update cleanup metrics
    cleanupStats.bytesStored = bytesStored;
    cleanupStats.bytesSaved = compressionSavings;

    // Save the cleaned cache and updated stats
    await chrome.storage.local.set({
      [APP_CONFIG.STORAGE_KEYS.TRANSLATION_CACHE]: newCache,
      [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: stats
    });

    console.log(`Cache cleanup complete. Kept ${entriesToKeep.length}/${cacheEntries.length} entries`);
    console.log(`Storage: ${(bytesStored / 1024).toFixed(2)} KB, Saved: ${(compressionSavings / 1024).toFixed(2)} KB`);
  } else {
    // We're under capacity, just update the stats
    stats.lastCleanup = now;

    // Update cleanup metrics
    cleanupStats.keptCount = validEntries.length;

    // Calculate storage metrics for reporting
    let bytesStored = 0;
    let compressionSavings = 0;

    for (const [_, entry] of validEntries) {
      const originalSize = entry.size || 0;
      const storedSize = entry.data ? entry.data.length : (entry.translation ? entry.translation.length : 0);

      bytesStored += storedSize;
      if (entry.compressed && originalSize > 0) {
        compressionSavings += (originalSize - storedSize);
      }
    }

    stats.bytesStored = bytesStored;
    stats.compressionSavings = compressionSavings;

    // Update cleanup metrics
    cleanupStats.bytesStored = bytesStored;
    cleanupStats.bytesSaved = compressionSavings;

    await chrome.storage.local.set({
      [APP_CONFIG.STORAGE_KEYS.CACHE_STATS]: stats
    });

    console.log(`Cache cleanup: All ${validEntries.length} entries are valid and under capacity`);
  }

  // Return cleanup stats if requested
  if (returnStats) {
    return {
      entriesInitial: cleanupStats.initialCount,
      entriesExpired: cleanupStats.expiredCount,
      entriesRemoved: cleanupStats.removedCount,
      entriesKept: cleanupStats.keptCount,
      storageKB: (cleanupStats.bytesStored / 1024).toFixed(2),
      savedKB: (cleanupStats.bytesSaved / 1024).toFixed(2)
    };
  }
}

function setupRateLimiting() {
  chrome.storage.local.get(['apiRequestTimestamps'], function (result) {
    if (!result.apiRequestTimestamps) {
      chrome.storage.local.set({ apiRequestTimestamps: [] });
    }
  });
}

async function checkRateLimit() {
  const now = Date.now();
  const oneMinuteAgo = now - 60 * 1000;

  const result = await chrome.storage.local.get(['apiRequestTimestamps']);
  const timestamps = result.apiRequestTimestamps || [];

  const validTimestamps = timestamps.filter(timestamp => timestamp > oneMinuteAgo);
  await chrome.storage.local.set({ apiRequestTimestamps: validTimestamps });

  return validTimestamps.length < APP_CONFIG.SECURITY.API_REQUEST_RATE_LIMIT;
}

async function recordApiRequest() {
  const now = Date.now();

  const result = await chrome.storage.local.get(['apiRequestTimestamps']);
  const timestamps = result.apiRequestTimestamps || [];

  timestamps.push(now);
  await chrome.storage.local.set({ apiRequestTimestamps: timestamps });
}

initializeExtension();
