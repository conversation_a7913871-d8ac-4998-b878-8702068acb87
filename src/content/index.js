const popup = document.createElement('div');
popup.className = 'gemini-translator-popup';
popup.style.display = 'none';
// Ensure high z-index to avoid conflicts
popup.style.zIndex = '2147483647';
popup.style.position = 'absolute';

// Try to use Shadow DOM for better CSP resistance
let useShadowDOM = false;
try {
  if (popup.attachShadow) {
    const shadow = popup.attachShadow({ mode: 'closed' });
    popup.shadowRoot = shadow;
    useShadowDOM = true;
    console.log('Shadow DOM enabled for better CSP resistance');
  }
} catch (error) {
  console.warn('Shadow DOM not available, using regular DOM:', error);
}

document.body.appendChild(popup);
console.log('Gemini Translator popup element created and added to body', { useShadowDOM });

function initializeContentScript() {
  console.log('Initializing Gemini Translator content script on:', window.location.href);

  document.addEventListener('mouseup', handleSelectionEvent);
  document.addEventListener('keyup', handleSelectionEvent);

  chrome.runtime.onMessage.addListener((message) => {
    handleBackgroundMessages(message);
  });

  // Test if content script is working
  console.log('Content script initialized successfully');

  // Add test functions to global scope for debugging
  window.testGeminiPopup = function () {
    console.log('Testing Gemini popup display...');
    showTranslationPopup('This is a test translation to verify the popup is working correctly.', { x: 100, y: 100 });
  };

  window.testGeminiButton = function () {
    console.log('Testing Gemini button display...');
    showTranslationButton({ x: 200, y: 200 }, 'Test text selection');
  };

  window.debugGeminiExtension = function () {
    console.log('=== Gemini Extension Debug Info ===');
    console.log('Popup element:', popup);
    console.log('Popup in DOM:', document.contains(popup));
    console.log('Popup computed style:', window.getComputedStyle(popup));
    console.log('Shadow DOM enabled:', useShadowDOM);
    console.log('Page URL:', window.location.href);
    console.log('Content script loaded:', true);

    // Test CSP again
    detectCSPRestrictions();

    // Test popup visibility
    const rect = popup.getBoundingClientRect();
    console.log('Popup bounding rect:', rect);

    return {
      popup,
      useShadowDOM,
      rect,
      inDOM: document.contains(popup)
    };
  };

  console.log('Test functions added: window.testGeminiPopup(), window.testGeminiButton(), window.debugGeminiExtension()');

  // Detect CSP restrictions
  detectCSPRestrictions();
}

/**
 * Detect if the page has CSP restrictions that might interfere with the extension
 */
function detectCSPRestrictions() {
  try {
    // Test 1: Try to create a style element
    const testStyle = document.createElement('style');
    testStyle.textContent = '/* CSP test */';
    document.head.appendChild(testStyle);
    document.head.removeChild(testStyle);
    console.log('CSP Test: Style element creation - PASSED');
  } catch (error) {
    console.warn('CSP Test: Style element creation - FAILED', error);
  }

  try {
    // Test 2: Try to set innerHTML with style
    const testDiv = document.createElement('div');
    testDiv.innerHTML = '<div style="color: red;">CSP Test</div>';
    console.log('CSP Test: innerHTML with inline styles - PASSED');
  } catch (error) {
    console.warn('CSP Test: innerHTML with inline styles - FAILED', error);
  }

  try {
    // Test 3: Check for CSP headers
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (cspMeta) {
      console.warn('CSP detected via meta tag:', cspMeta.content);
    }
  } catch (error) {
    console.warn('CSP Test: Meta tag check - FAILED', error);
  }
}

function handleSelectionEvent(event) {
  console.log('Selection event triggered:', event.type);
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();
  console.log('Selected text:', selectedText);

  if (!selectedText) {
    hidePopup();
    return;
  }

  let position;
  try {
    if (event && event.type === 'mouseup' && event.clientX != null && event.clientY != null) {
      position = {
        x: event.clientX + window.scrollX + 10,
        y: event.clientY + window.scrollY
      };
    } else if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      position = {
        x: rect.left + window.scrollX + (rect.width / 2),
        y: rect.bottom + window.scrollY
      };
    } else {
      hidePopup();
      return;
    }
  } catch (error) {
    console.error("Error getting selection position:", error);
    hidePopup();
    return;
  }


  showTranslationButton(position, selectedText);
}

function handleBackgroundMessages(message) {
  console.log('Content script received message:', message);

  switch (message.action) {
    case 'showTranslation':
      console.log('Showing translation popup with:', message.translation, message.position);
      showTranslationPopup(message.translation, message.position);
      break;
    case 'showError':
      console.log('Showing error popup with:', message.error, message.position);
      showErrorPopup(message.error, message.position);
      break;
    case 'translateText':
      const selection = window.getSelection();
      if (selection.toString().trim() === message.text.trim()) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        const position = {
          x: rect.left + window.scrollX + (rect.width / 2),
          y: rect.bottom + window.scrollY
        };
        sendMessageToBackground({
          action: 'translate',
          text: message.text,

          position
        });
      }
      break;
    case 'updateFontStyle':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    case 'updateFontSize':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    default:

      break;
  }
}

function sendMessageToBackground(message) {
  chrome.runtime.sendMessage(message);
}

function showTranslationButton(position, text) {
  let button = document.getElementById('gemini-translator-button');

  if (!button) {
    button = document.createElement('button');
    button.id = 'gemini-translator-button';
    button.className = 'gemini-translator-button';
    document.body.appendChild(button);

    button.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M5 8l6 6"></path>
        <path d="M4 14l6-6 2-3"></path>
        <path d="M2 5h12"></path>
        <path d="M7 2h1"></path>
        <path d="M22 22l-5-10-5 10"></path>
        <path d="M14 18h6"></path>
      </svg>
    `;
  }

  button.style.left = `${position.x}px`;
  button.style.top = `${position.y}px`;
  button.style.display = 'flex';

  button.onclick = function () {
    console.log('Translation button clicked with text:', text);
    if (window.showLoading) {
      window.showLoading(position);
    }

    sendMessageToBackground({
      action: 'translate',
      text: text,
      position: position
    });
    button.classList.add('loading');
  };
}

function formatAndSanitizeMarkdown(text) {
  if (!text || typeof text !== 'string') {
    return '<p>Translation not available.</p>';
  }

  try {
    marked.setOptions({
      sanitize: true,
      silent: true,
      headerIds: false,
      mangle: false
    });

    const rawHtml = marked.parse(text);
    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
      ALLOWED_TAGS: [
        // Basic formatting
        'p', 'br', 'strong', 'em', 'i', 'b', 'code', 'pre',
        // Lists
        'ul', 'ol', 'li',
        // Headings (limited to h3 and below for UI consistency)
        'h3', 'h4', 'h5', 'h6',
        // Other elements
        'blockquote', 'span'
      ],
      ALLOWED_ATTR: [],
      ADD_ATTR: ['class'],
      FORBID_TAGS: ['style', 'script', 'iframe', 'frame', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button'],
      FORBID_ATTR: ['style', 'onerror', 'onload', 'onclick', 'onmouseover'],
      ALLOW_DATA_ATTR: false,
      USE_PROFILES: { html: true },
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
      WHOLE_DOCUMENT: false,
      FORCE_BODY: true
    });

    return sanitizedHtml || '<p>Translation not available.</p>';
  } catch (error) {
    console.error('Error formatting markdown:', error);
    return '<p>Error formatting translation.</p>';
  }
}

function generatePopupStyles(fontFamily, fontSize) {
  const size = parseInt(fontSize, 10) || 15;
  const lineHeight = size > 20 ? 1.8 : 1.6;
  const letterSpacing = fontFamily && fontFamily.includes('Pacifico') ? '0.5px' : 'normal';

  return `
    @import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Cabin:ital,wght@0,400..700;1,400..700&family=Comfortaa:wght@300..700&family=Crimson+Pro:ital,wght@0,200..900;1,200..900&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Fraunces:ital,opsz,wght@0,9..144,100..900;1,9..144,100..900&family=Geologica:wght@100..900&family=Lexend+Deca:wght@100..900&family=Literata:ital,opsz,wght@0,7..72,200..900;1,7..72,200..900&family=Mali:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Manrope:wght@200..800&family=Maven+Pro:wght@400..900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Petrona:ital,wght@0,100..900;1,100..900&family=Questrial&family=Quicksand:wght@300..700&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Space+Grotesk:wght@300..700&family=Varela+Round&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');

    .gemini-translator-card {
      background-color: #0a1929 !important;
    }
    .gemini-translator-card-header h3 {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      font-weight: 600 !important;
      line-height: 1 !important;
    }
    .gemini-translator-translation,
    .gemini-translator-translation p,
    .gemini-translator-translation h1,
    .gemini-translator-translation h2,
    .gemini-translator-translation h3,
    .gemini-translator-translation li,
    .gemini-translator-translation code,
    .gemini-translator-translation pre {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      font-weight: 400!important;
      line-height: ${lineHeight} !important;
      letter-spacing: ${letterSpacing} !important;
      margin: 0 !important;
      color: #94a3b8 !important;
    }
    .gemini-translator-content {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      gap: 0 !important;
      color: #94a3b8 !important;
    }
    .gemini-translator-translation {
      font-family: ${fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif'} !important;
      font-size: ${size}px!important;
      padding: 1px 4px !important;
      color: #94a3b8 !important;
    }
    .gemini-translator-translation p {
      color: #94a3b8 !important;
    }
    .gemini-translator-translation pre {
      white-space: pre-wrap; /* Wrap long code lines */
      word-wrap: break-word;
      background-color: rgba(255, 255, 255, 0.05); /* Slight background for code blocks */
      padding: 5px;
      border-radius: 3px;
      color: #94a3b8 !important;
    }
    .gemini-translator-translation code {
       background-color: rgba(255, 255, 255, 0.05); /* Slight background for inline code */
       padding: 1px 3px;
       border-radius: 3px;
       color: #94a3b8 !important;
    }
  `;
}

/**
 * CSP-resistant method to apply styles directly to elements
 * instead of using inline <style> tags
 */
function applyPopupStyles(element, fontFamily, fontSize) {
  const size = parseInt(fontSize, 10) || 15;

  // Apply styles directly to the element (more CSP-resistant)
  element.style.fontFamily = fontFamily;
  element.style.fontSize = `${size}px`;
  element.style.lineHeight = size > 20 ? '1.8' : '1.6';
  element.style.color = '#94a3b8';
  element.style.overflowWrap = 'break-word';
  element.style.webkitFontSmoothing = 'antialiased';
  element.style.mozOsxFontSmoothing = 'grayscale';
  element.style.textRendering = 'optimizeLegibility';
  element.style.position = 'relative';
  element.style.overflow = 'hidden';
  element.style.padding = '4px';
  element.style.margin = '2px';
  element.style.background = 'rgba(33, 40, 48, 0.1)';
  element.style.backdropFilter = 'blur(8px)';
  element.style.borderRadius = '6px';

  // Apply typography styles to child elements after they're added
  setTimeout(() => {
    const paragraphs = element.querySelectorAll('p');
    paragraphs.forEach(p => {
      p.style.margin = '0';
      p.style.color = '#94a3b8';
      p.style.fontSize = '0.875rem';
    });

    const headings = element.querySelectorAll('h1, h2, h3');
    headings.forEach(h => {
      h.style.margin = '8px 0';
      h.style.fontWeight = '600';
      h.style.color = '#94a3b8';
    });

    const code = element.querySelectorAll('code');
    code.forEach(c => {
      c.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      c.style.padding = '1px 3px';
      c.style.borderRadius = '3px';
      c.style.color = '#94a3b8';
    });
  }, 10);
}

function showTranslationPopup(translation, position, forcePosition = null) {
  console.log('showTranslationPopup called with:', { translation, position, forcePosition });

  const button = document.getElementById('gemini-translator-button');
  if (button) {
    button.classList.remove('loading');
    button.style.display = 'none';
  }

  const formattedTranslation = formatAndSanitizeMarkdown(translation);
  console.log('Formatted translation:', formattedTranslation);

  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    // CSP-resistant approach: Use DOM methods instead of innerHTML with inline styles
    try {
      const targetElement = popup.shadowRoot || popup;

      // Clear existing content
      targetElement.innerHTML = '';

      // If using Shadow DOM, add styles to shadow root
      if (popup.shadowRoot) {
        const shadowStyle = document.createElement('style');
        shadowStyle.textContent = `
          @import url('${chrome.runtime.getURL('src/content/styles.css')}');
        `;
        popup.shadowRoot.appendChild(shadowStyle);
      }

      // Create elements using DOM methods (CSP-friendly)
      const content = document.createElement('div');
      content.className = 'gemini-translator-content';

      const card = document.createElement('div');
      card.className = 'gemini-translator-card';

      const translationDiv = document.createElement('div');
      translationDiv.className = 'gemini-translator-translation';

      // Apply styles directly to element (more CSP-resistant than inline <style> tags)
      applyPopupStyles(translationDiv, fontFamily, fontSize);

      // Set content safely
      translationDiv.innerHTML = formattedTranslation;

      // Assemble the popup
      card.appendChild(translationDiv);
      content.appendChild(card);
      targetElement.appendChild(content);

    } catch (cspError) {
      console.warn('CSP-resistant method failed, falling back to basic approach:', cspError);

      // Try multiple fallback approaches
      try {
        // Fallback 1: Use regular popup without Shadow DOM
        popup.innerHTML = `
          <div class="gemini-translator-content">
            <div class="gemini-translator-card">
              <div class="gemini-translator-translation">
                ${formattedTranslation}
              </div>
            </div>
          </div>
        `;
      } catch (fallback1Error) {
        console.warn('Fallback 1 failed, trying fallback 2:', fallback1Error);

        try {
          // Fallback 2: Create elements manually without innerHTML
          popup.innerHTML = '';
          const content = document.createElement('div');
          content.className = 'gemini-translator-content';
          const card = document.createElement('div');
          card.className = 'gemini-translator-card';
          const translationDiv = document.createElement('div');
          translationDiv.className = 'gemini-translator-translation';
          translationDiv.textContent = translation; // Use textContent as last resort

          card.appendChild(translationDiv);
          content.appendChild(card);
          popup.appendChild(content);
        } catch (fallback2Error) {
          console.error('All fallback methods failed:', fallback2Error);
          // Last resort: show a simple text popup
          popup.innerHTML = '';
          popup.textContent = translation;
          popup.style.background = '#18385a';
          popup.style.color = '#94a3b8';
          popup.style.padding = '10px';
          popup.style.borderRadius = '6px';
          popup.style.fontSize = '14px';
          popup.style.maxWidth = '400px';
          popup.style.overflowWrap = 'break-word';
        }
      }
    }

    console.log('Popup HTML set, positioning popup...');
    positionPopup(position);
    popup.style.display = 'block';
    console.log('Popup display set to block, final popup style:', {
      display: popup.style.display,
      left: popup.style.left,
      top: popup.style.top,
      visibility: popup.style.visibility,
      zIndex: popup.style.zIndex,
      position: popup.style.position
    });

    // Additional debugging - check if popup is actually visible
    const rect = popup.getBoundingClientRect();
    console.log('Popup bounding rect:', rect);
    console.log('Popup computed style:', window.getComputedStyle(popup));

    // Check if popup is in the DOM
    console.log('Popup parent:', popup.parentElement);
    console.log('Popup in document:', document.contains(popup));

    document.addEventListener('mousedown', closeOnClickOutside);
  });
}

function showErrorPopup(error, position) {
  popup.innerHTML = `
    <div class="gemini-translator-content gemini-translator-error">
      <div class="gemini-translator-header">
        <span>Translation Error</span>
        <button class="gemini-translator-close">&times;</button>
      </div>
      <div class="gemini-translator-message">${error}</div>
    </div>
  `;

  const closeBtn = popup.querySelector('.gemini-translator-close');
  if (closeBtn) {
    closeBtn.addEventListener('click', hidePopup);
  }

  positionPopup(position);
  popup.style.display = 'block';

  document.addEventListener('mousedown', closeOnClickOutside);
}

function updatePopupStyles() {
  chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
    const fontFamily = fontResult.fontFamily || '-apple-system, BlinkMacSystemFont, system-ui, sans-serif';
    const fontSize = fontResult.fontSize || '15';

    const styles = generatePopupStyles(fontFamily, fontSize);

    const styleElement = popup.querySelector('style');
    if (styleElement) {
      styleElement.textContent = styles;
    } else {
      const newStyle = document.createElement('style');
      newStyle.textContent = styles;
      popup.querySelector('.gemini-translator-translation').prepend(newStyle);
    }
  });
}

function hidePopup() {
  popup.style.display = 'none';
  document.removeEventListener('mousedown', closeOnClickOutside);

  const button = document.getElementById('gemini-translator-button');
  if (button) {
    button.style.display = 'none';
  }
}

function positionPopup(position) {
  chrome.storage.local.get(['autoPosition', 'defaultPosition'], function (prefs) {
    const autoPositionEnabled = typeof prefs.autoPosition === 'boolean' ? prefs.autoPosition : true;
    const defaultPositionValue = typeof prefs.defaultPosition === 'string' ? prefs.defaultPosition : 'below';

    // Set initial position to make popup visible so we can measure it
    popup.style.left = '0px';
    popup.style.top = '0px';

    // Use a setTimeout to ensure the popup has been rendered and has dimensions
    setTimeout(() => {
      // Get actual dimensions after rendering
      const popupWidth = popup.offsetWidth || 300;
      const popupHeight = popup.offsetHeight || 150;

      let left = position.x - (popupWidth / 2);
      let top;

      // Set initial position based on settings
      if (autoPositionEnabled) {
        // Default position below text
        top = position.y + 10;

        // Auto-adjust if it would go off-screen
        if (top + popupHeight > window.scrollY + window.innerHeight - 10) {
          // Not enough room below, try above
          top = position.y - popupHeight - 10;

          // If still not enough room, position at the top of viewport with scroll
          if (top < window.scrollY + 10) {
            top = window.scrollY + 10;
          }
        }
      } else {
        // Use the specified default position
        switch (defaultPositionValue) {
          case 'above':
            top = position.y - popupHeight - 10;
            break;
          case 'cursor':
            // Position top edge at cursor Y
            top = position.y;
            break;
          case 'below':
          default:
            // Position below text (default)
            top = position.y + 10;
            break;
        }

        // Safety checks to ensure popup stays within viewport
        if (top + popupHeight > window.scrollY + window.innerHeight - 10) {
          top = window.scrollY + window.innerHeight - popupHeight - 10;
        }
        if (top < window.scrollY + 10) {
          top = window.scrollY + 10;
        }
      }

      // Ensure popup stays within horizontal bounds
      if (left < 10) {
        left = 10;
      }
      if (left + popupWidth > window.innerWidth - 10) {
        left = window.innerWidth - popupWidth - 10;
      }

      // Apply the calculated position
      popup.style.left = `${left}px`;
      popup.style.top = `${top}px`;
    }, 10); // Slightly longer timeout to ensure rendering
  });
}

function closeOnClickOutside(event) {
  if (!popup.contains(event.target) && event.target.id !== 'gemini-translator-button') {
    hidePopup();
  }
}

initializeContentScript();
