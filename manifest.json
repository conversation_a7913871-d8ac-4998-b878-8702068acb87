{"manifest_version": 3, "name": "Gemini Translator", "version": "1.1.0", "description": "Translate selected text using Google's Gemini 2.0 Flash API", "action": {"default_popup": "src/popup/index.html", "default_icon": {"16": "assets/icons/icon16.png", "48": "assets/icons/icon48.png", "128": "assets/icons/icon128.png"}}, "icons": {"16": "assets/icons/icon16.png", "48": "assets/icons/icon48.png", "128": "assets/icons/icon128.png"}, "permissions": ["activeTab", "scripting", "storage", "contextMenus", "clipboardRead"], "host_permissions": ["https://generativelanguage.googleapis.com/*"], "background": {"service_worker": "src/background/index.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["src/lib/marked.min.js", "src/lib/purify.min.js", "src/content/index.js"], "css": ["src/content/styles.css"]}], "content_security_policy": {"extension_pages": "default-src 'self'; connect-src https://generativelanguage.googleapis.com; script-src 'self'; object-src 'none'; img-src 'self' data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src-attr 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com"}, "web_accessible_resources": [{"resources": ["assets/icons/*", "src/content/styles.css"], "matches": ["<all_urls>"]}]}